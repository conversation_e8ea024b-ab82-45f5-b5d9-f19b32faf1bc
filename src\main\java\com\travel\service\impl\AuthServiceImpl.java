package com.travel.service.impl;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.travel.common.BusinessException;
import com.travel.common.ResultCode;
import com.travel.dto.auth.PhoneAuthRequest;
import com.travel.dto.auth.WeChatLoginRequest;
import com.travel.entity.User;
import com.travel.service.AuthService;
import com.travel.service.UserService;
import com.travel.utils.JwtUtils;
import com.travel.utils.WeChatUtils;
import com.travel.vo.auth.LoginResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final UserService userService;
    private final WeChatUtils weChatUtils;
    private final JwtUtils jwtUtils;
    private final StringRedisTemplate redisTemplate;

    private static final String USER_SESSION_KEY = "user:session:";
    private static final String USER_TOKEN_KEY = "user:token:";

    @Override
    public LoginResponse wechatLogin(WeChatLoginRequest request) {
        try {
            // 1. 通过code获取微信session信息
            WxMaJscode2SessionResult sessionInfo = weChatUtils.getSessionInfo(request.getCode());
            String openid = sessionInfo.getOpenid();
            String sessionKey = sessionInfo.getSessionKey();

            if (StringUtils.isBlank(openid)) {
                throw BusinessException.of(ResultCode.WECHAT_LOGIN_FAILED, "获取用户openid失败");
            }

            // 2. 创建或更新用户信息
            User user = userService.createOrUpdateUser(
                    openid, 
                    request.getNickname(), 
                    request.getAvatarUrl(), 
                    request.getRegion()
            );

            // 3. 生成JWT令牌
            String token = jwtUtils.generateToken(user.getUserId(), openid);

            // 4. 缓存session信息
            redisTemplate.opsForValue().set(
                    USER_SESSION_KEY + user.getUserId(), 
                    sessionKey, 
                    7, 
                    TimeUnit.DAYS
            );

            // 5. 缓存token
            redisTemplate.opsForValue().set(
                    USER_TOKEN_KEY + user.getUserId(), 
                    token, 
                    7, 
                    TimeUnit.DAYS
            );

            log.info("用户登录成功: userId={}, openid={}", user.getUserId(), openid);

            // 6. 返回登录响应
            return new LoginResponse(
                    token,
                    user.getUserId(),
                    user.getNickname(),
                    user.getAvatarUrl(),
                    StringUtils.isBlank(user.getPhone())
            );

        } catch (Exception e) {
            log.error("微信登录失败", e);
            throw BusinessException.of(ResultCode.WECHAT_LOGIN_FAILED, e.getMessage());
        }
    }

    @Override
    public void authorizePhone(Integer userId, PhoneAuthRequest request) {
        try {
            // 1. 获取手机号信息
            WxMaPhoneNumberInfo phoneInfo = weChatUtils.getPhoneNumber(request.getCode());
            String phoneNumber = phoneInfo.getPhoneNumber();

            if (StringUtils.isBlank(phoneNumber)) {
                throw BusinessException.of(ResultCode.PHONE_REQUIRED, "获取手机号失败");
            }

            // 2. 更新用户手机号
            userService.updatePhone(userId, phoneNumber);

            log.info("用户手机号授权成功: userId={}, phone={}", userId, phoneNumber);

        } catch (Exception e) {
            log.error("手机号授权失败: userId={}", userId, e);
            throw BusinessException.of(ResultCode.PHONE_REQUIRED, e.getMessage());
        }
    }

    @Override
    public LoginResponse refreshToken(String token) {
        // 1. 验证当前token
        if (!jwtUtils.validateToken(token)) {
            throw BusinessException.of(ResultCode.TOKEN_INVALID);
        }

        // 2. 获取用户信息
        Integer userId = jwtUtils.getUserIdFromToken(token);
        String openid = jwtUtils.getOpenidFromToken(token);
        
        if (userId == null || StringUtils.isBlank(openid)) {
            throw BusinessException.of(ResultCode.TOKEN_INVALID);
        }

        User user = userService.getUserDetail(userId);

        // 3. 生成新token
        String newToken = jwtUtils.generateToken(userId, openid);

        // 4. 更新缓存
        redisTemplate.opsForValue().set(
                USER_TOKEN_KEY + userId, 
                newToken, 
                7, 
                TimeUnit.DAYS
        );

        log.info("刷新token成功: userId={}", userId);

        return new LoginResponse(
                newToken,
                user.getUserId(),
                user.getNickname(),
                user.getAvatarUrl(),
                StringUtils.isBlank(user.getPhone())
        );
    }

    @Override
    public void logout(Integer userId) {
        // 清除缓存
        redisTemplate.delete(USER_SESSION_KEY + userId);
        redisTemplate.delete(USER_TOKEN_KEY + userId);
        
        log.info("用户登出成功: userId={}", userId);
    }
}
