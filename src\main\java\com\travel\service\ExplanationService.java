package com.travel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.travel.entity.AttractionCategory;
import com.travel.entity.ExplanationPoint;

import java.util.List;

/**
 * 讲解服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface ExplanationService extends IService<ExplanationPoint> {

    /**
     * 根据产品ID获取分类列表
     */
    List<AttractionCategory> getCategoriesByProduct(Integer productId);

    /**
     * 根据分类ID获取讲解点列表
     */
    List<ExplanationPoint> getPointsByCategory(Integer categoryId);

    /**
     * 获取讲解点详情
     */
    ExplanationPoint getPointDetail(Integer pointId);

    /**
     * 根据产品ID获取所有讲解点（按分类分组）
     */
    List<AttractionCategory> getProductExplanation(Integer productId);
}
