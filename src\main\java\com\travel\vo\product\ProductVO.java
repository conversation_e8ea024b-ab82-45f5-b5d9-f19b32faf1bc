package com.travel.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品信息VO
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@Schema(description = "产品信息")
public class ProductVO {

    @Schema(description = "产品ID", example = "1")
    private Integer productId;

    @Schema(description = "产品名称", example = "故宫博物院讲解包")
    private String name;

    @Schema(description = "产品类型(1讲解包 2景点)", example = "1")
    private Integer type;

    @Schema(description = "产品类型名称", example = "讲解包")
    private String typeName;

    @Schema(description = "封面图URL", example = "https://example.com/cover.jpg")
    private String coverUrl;

    @Schema(description = "产品描述", example = "专业讲解故宫历史文化")
    private String description;

    @Schema(description = "价格", example = "29.90")
    private BigDecimal price;

    @Schema(description = "讲解时长", example = "120分钟")
    private String duration;

    @Schema(description = "城市信息")
    private CityInfo city;

    @Schema(description = "讲师信息")
    private LecturerInfo lecturer;

    @Schema(description = "标签列表")
    private List<String> tags;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Data
    @Schema(description = "城市信息")
    public static class CityInfo {
        @Schema(description = "城市ID", example = "1")
        private Integer cityId;

        @Schema(description = "城市名称", example = "北京")
        private String name;
    }

    @Data
    @Schema(description = "讲师信息")
    public static class LecturerInfo {
        @Schema(description = "讲师ID", example = "1")
        private Integer lecturerId;

        @Schema(description = "讲师姓名", example = "张教授")
        private String name;

        @Schema(description = "讲师头像", example = "https://example.com/avatar.jpg")
        private String avatarUrl;

        @Schema(description = "头衔", example = "故宫博物院研究员")
        private String title;
    }
}
