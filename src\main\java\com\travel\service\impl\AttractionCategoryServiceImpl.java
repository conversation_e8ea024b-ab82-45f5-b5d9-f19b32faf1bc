package com.travel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.common.BusinessException;
import com.travel.common.ResultCode;
import com.travel.entity.AttractionCategory;
import com.travel.mapper.AttractionCategoryMapper;
import com.travel.service.AttractionCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 景区分类服务实现
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AttractionCategoryServiceImpl extends ServiceImpl<AttractionCategoryMapper, AttractionCategory> 
        implements AttractionCategoryService {

    @Override
    public List<AttractionCategory> getCategoriesByProductId(Integer productId) {
        LambdaQueryWrapper<AttractionCategory> wrapper = new LambdaQueryWrapper<AttractionCategory>()
                .eq(AttractionCategory::getProductId, productId)
                .orderByAsc(AttractionCategory::getSortOrder)
                .orderByAsc(AttractionCategory::getCategoryId);
        return this.list(wrapper);
    }

    @Override
    public AttractionCategory getCategoryDetail(Integer categoryId) {
        AttractionCategory category = this.getById(categoryId);
        if (category == null) {
            throw BusinessException.of(ResultCode.NOT_FOUND, "分类不存在");
        }
        return category;
    }
}
