package com.travel.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 微信登录请求DTO
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@Schema(description = "微信登录请求")
public class WeChatLoginRequest {

    @Schema(description = "微信登录code", example = "081234567890")
    @NotBlank(message = "登录code不能为空")
    private String code;

    @Schema(description = "用户昵称", example = "张三")
    private String nickname;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "用户地区", example = "北京市")
    private String region;
}
