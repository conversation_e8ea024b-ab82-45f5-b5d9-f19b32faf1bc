package com.travel.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.travel.entity.Product;

/**
 * 产品信息服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface ProductService extends IService<Product> {

    /**
     * 分页查询产品列表
     */
    IPage<Product> getProductPage(Integer current, Integer size, Integer cityId, Integer type, String keyword);

    /**
     * 获取产品详情
     */
    Product getProductDetail(Integer productId);

    /**
     * 根据城市ID获取产品列表
     */
    IPage<Product> getProductsByCity(Integer current, Integer size, Integer cityId);

    /**
     * 获取热门产品
     */
    IPage<Product> getHotProducts(Integer current, Integer size);

    /**
     * 搜索产品
     */
    IPage<Product> searchProducts(Integer current, Integer size, String keyword);
}
