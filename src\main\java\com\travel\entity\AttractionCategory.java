package com.travel.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 景区分类实体
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@TableName("attraction_category")
@Schema(description = "景区分类")
public class AttractionCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类ID")
    @TableId(value = "category_id", type = IdType.AUTO)
    private Integer categoryId;

    @Schema(description = "关联产品ID")
    @TableField("product_id")
    private Integer productId;

    @Schema(description = "分类名称")
    @TableField("name")
    private String name;

    @Schema(description = "排序序号")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
