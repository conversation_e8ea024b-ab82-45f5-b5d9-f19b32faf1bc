package com.travel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.travel.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 产品信息Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 分页查询产品列表（包含城市和讲师信息）
     */
    IPage<Product> selectProductPageWithDetails(Page<Product> page, 
                                               @Param("cityId") Integer cityId,
                                               @Param("type") Integer type,
                                               @Param("keyword") String keyword);
}
