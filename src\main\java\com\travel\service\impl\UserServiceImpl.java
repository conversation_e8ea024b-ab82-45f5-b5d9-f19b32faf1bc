package com.travel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.common.BusinessException;
import com.travel.common.ResultCode;
import com.travel.entity.User;
import com.travel.mapper.UserMapper;
import com.travel.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 用户信息服务实现
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    public User findByOpenid(String openid) {
        if (StringUtils.isBlank(openid)) {
            return null;
        }
        return this.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getOpenid, openid));
    }

    @Override
    public User createOrUpdateUser(String openid, String nickname, String avatarUrl, String region) {
        User existingUser = findByOpenid(openid);
        
        if (existingUser != null) {
            // 更新用户信息
            boolean needUpdate = false;
            if (StringUtils.isNotBlank(nickname) && !nickname.equals(existingUser.getNickname())) {
                existingUser.setNickname(nickname);
                needUpdate = true;
            }
            if (StringUtils.isNotBlank(avatarUrl) && !avatarUrl.equals(existingUser.getAvatarUrl())) {
                existingUser.setAvatarUrl(avatarUrl);
                needUpdate = true;
            }
            if (StringUtils.isNotBlank(region) && !region.equals(existingUser.getRegion())) {
                existingUser.setRegion(region);
                needUpdate = true;
            }
            
            if (needUpdate) {
                this.updateById(existingUser);
                log.info("更新用户信息: userId={}", existingUser.getUserId());
            }
            return existingUser;
        } else {
            // 创建新用户
            User newUser = new User();
            newUser.setOpenid(openid);
            newUser.setNickname(nickname);
            newUser.setAvatarUrl(avatarUrl);
            newUser.setRegion(region);
            newUser.setCreatedAt(LocalDateTime.now());
            
            this.save(newUser);
            log.info("创建新用户: userId={}, openid={}", newUser.getUserId(), openid);
            return newUser;
        }
    }

    @Override
    public void updatePhone(Integer userId, String phone) {
        User user = this.getById(userId);
        if (user == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND);
        }
        
        user.setPhone(phone);
        this.updateById(user);
        log.info("更新用户手机号: userId={}", userId);
    }

    @Override
    public User getUserDetail(Integer userId) {
        User user = this.getById(userId);
        if (user == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND);
        }
        return user;
    }
}
