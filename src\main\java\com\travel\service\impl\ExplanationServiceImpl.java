package com.travel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.common.BusinessException;
import com.travel.common.ResultCode;
import com.travel.entity.AttractionCategory;
import com.travel.entity.ExplanationPoint;
import com.travel.mapper.ExplanationPointMapper;
import com.travel.service.AttractionCategoryService;
import com.travel.service.ExplanationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 讲解服务实现
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExplanationServiceImpl extends ServiceImpl<ExplanationPointMapper, ExplanationPoint> 
        implements ExplanationService {

    private final AttractionCategoryService categoryService;

    @Override
    public List<AttractionCategory> getCategoriesByProduct(Integer productId) {
        return categoryService.getCategoriesByProductId(productId);
    }

    @Override
    public List<ExplanationPoint> getPointsByCategory(Integer categoryId) {
        LambdaQueryWrapper<ExplanationPoint> wrapper = new LambdaQueryWrapper<ExplanationPoint>()
                .eq(ExplanationPoint::getCategoryId, categoryId)
                .orderByAsc(ExplanationPoint::getSortOrder)
                .orderByAsc(ExplanationPoint::getPointId);
        return this.list(wrapper);
    }

    @Override
    public ExplanationPoint getPointDetail(Integer pointId) {
        ExplanationPoint point = this.getById(pointId);
        if (point == null) {
            throw BusinessException.of(ResultCode.NOT_FOUND, "讲解点不存在");
        }
        return point;
    }

    @Override
    public List<AttractionCategory> getProductExplanation(Integer productId) {
        // 获取产品的所有分类
        List<AttractionCategory> categories = getCategoriesByProduct(productId);
        
        // 为每个分类加载讲解点
        for (AttractionCategory category : categories) {
            List<ExplanationPoint> points = getPointsByCategory(category.getCategoryId());
            // 这里可以设置一个临时字段来存储讲解点，或者使用VO类
            // category.setExplanationPoints(points);
        }
        
        return categories;
    }
}
