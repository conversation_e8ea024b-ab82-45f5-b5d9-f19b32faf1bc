package com.travel.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 手机号授权请求DTO
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@Schema(description = "手机号授权请求")
public class PhoneAuthRequest {

    @Schema(description = "手机号授权code", example = "081234567890")
    @NotBlank(message = "手机号授权code不能为空")
    private String code;
}
