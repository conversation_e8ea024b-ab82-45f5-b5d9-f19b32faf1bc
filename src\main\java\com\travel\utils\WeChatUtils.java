package com.travel.utils;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Component;

/**
 * 微信工具类
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WeChatUtils {

    private final WxMaService wxMaService;

    /**
     * 通过code获取session信息
     */
    public WxMaJscode2SessionResult getSessionInfo(String code) {
        try {
            return wxMaService.getUserService().getSessionInfo(code);
        } catch (WxErrorException e) {
            log.error("获取微信session信息失败: {}", e.getMessage());
            throw new RuntimeException("微信登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取手机号信息
     */
    public WxMaPhoneNumberInfo getPhoneNumber(String code) {
        try {
            return wxMaService.getUserService().getPhoneNoInfo(code);
        } catch (WxErrorException e) {
            log.error("获取微信手机号失败: {}", e.getMessage());
            throw new RuntimeException("获取手机号失败: " + e.getMessage());
        }
    }

    /**
     * 验证数据签名
     */
    public boolean checkSignature(String signature, String rawData, String sessionKey) {
        try {
            return wxMaService.getUserService().checkUserInfo(sessionKey, rawData, signature);
        } catch (WxErrorException e) {
            log.error("验证微信数据签名失败: {}", e.getMessage());
            return false;
        }
    }
}
