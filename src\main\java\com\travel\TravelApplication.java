package com.travel;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 旅游讲解小程序后端服务启动类
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@SpringBootApplication
@MapperScan("com.travel.mapper")
public class TravelApplication {

    public static void main(String[] args) {
        SpringApplication.run(TravelApplication.class, args);
        System.out.println("=================================");
        System.out.println("旅游讲解小程序后端服务启动成功！");
        System.out.println("API文档地址: http://localhost:8080/api/doc.html");
        System.out.println("=================================");
    }
}
