package com.travel.controller;

import com.travel.common.Result;
import com.travel.entity.AttractionCategory;
import com.travel.entity.ExplanationPoint;
import com.travel.service.ExplanationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 讲解控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "讲解管理", description = "讲解相关接口")
@RestController
@RequestMapping("/explanation")
@RequiredArgsConstructor
public class ExplanationController {

    private final ExplanationService explanationService;

    @Operation(summary = "获取产品分类", description = "根据产品ID获取景区分类列表")
    @GetMapping("/categories/{productId}")
    public Result<List<AttractionCategory>> getCategoriesByProduct(
            @Parameter(description = "产品ID", required = true) @PathVariable Integer productId) {
        List<AttractionCategory> categories = explanationService.getCategoriesByProduct(productId);
        return Result.success(categories);
    }

    @Operation(summary = "获取分类讲解点", description = "根据分类ID获取讲解点列表")
    @GetMapping("/points/{categoryId}")
    public Result<List<ExplanationPoint>> getPointsByCategory(
            @Parameter(description = "分类ID", required = true) @PathVariable Integer categoryId) {
        List<ExplanationPoint> points = explanationService.getPointsByCategory(categoryId);
        return Result.success(points);
    }

    @Operation(summary = "获取讲解点详情", description = "根据讲解点ID获取详细信息")
    @GetMapping("/point/{pointId}")
    public Result<ExplanationPoint> getPointDetail(
            @Parameter(description = "讲解点ID", required = true) @PathVariable Integer pointId) {
        ExplanationPoint point = explanationService.getPointDetail(pointId);
        return Result.success(point);
    }

    @Operation(summary = "获取产品完整讲解", description = "获取产品的完整讲解结构（分类+讲解点）")
    @GetMapping("/product/{productId}")
    public Result<List<AttractionCategory>> getProductExplanation(
            @Parameter(description = "产品ID", required = true) @PathVariable Integer productId) {
        List<AttractionCategory> explanation = explanationService.getProductExplanation(productId);
        return Result.success(explanation);
    }
}
