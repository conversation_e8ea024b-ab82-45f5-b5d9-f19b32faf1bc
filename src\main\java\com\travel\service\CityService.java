package com.travel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.travel.entity.City;

import java.util.List;

/**
 * 城市信息服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface CityService extends IService<City> {

    /**
     * 获取所有城市列表
     */
    List<City> getAllCities();

    /**
     * 获取城市详情
     */
    City getCityDetail(Integer cityId);

    /**
     * 根据名称搜索城市
     */
    List<City> searchCitiesByName(String name);
}
