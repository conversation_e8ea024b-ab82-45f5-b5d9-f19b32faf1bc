package com.travel.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应码枚举
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 通用响应码
    SUCCESS(200, "操作成功"),
    ERROR(500, "系统异常"),
    PARAM_ERROR(400, "参数错误"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    
    // 用户相关
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_DISABLED(1002, "用户已被禁用"),
    USER_ALREADY_EXISTS(1003, "用户已存在"),
    
    // 认证相关
    LOGIN_FAILED(2001, "登录失败"),
    TOKEN_INVALID(2002, "令牌无效"),
    TOKEN_EXPIRED(2003, "令牌已过期"),
    WECHAT_LOGIN_FAILED(2004, "微信登录失败"),
    PHONE_REQUIRED(2005, "需要手机号授权"),
    
    // 业务相关
    PRODUCT_NOT_FOUND(3001, "产品不存在"),
    PRODUCT_UNAVAILABLE(3002, "产品不可用"),
    ORDER_NOT_FOUND(3003, "订单不存在"),
    ORDER_STATUS_ERROR(3004, "订单状态错误"),
    INSUFFICIENT_STOCK(3005, "库存不足"),
    PAYMENT_FAILED(3006, "支付失败"),
    
    // 文件相关
    FILE_UPLOAD_FAILED(4001, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(4002, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(4003, "文件大小超出限制"),
    
    // 限流相关
    RATE_LIMIT_EXCEEDED(5001, "请求过于频繁，请稍后再试");

    private final Integer code;
    private final String message;
}
