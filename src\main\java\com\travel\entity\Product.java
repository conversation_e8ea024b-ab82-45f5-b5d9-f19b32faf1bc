package com.travel.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品信息实体
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@TableName("product")
@Schema(description = "产品信息")
public class Product implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "产品ID")
    @TableId(value = "product_id", type = IdType.AUTO)
    private Integer productId;

    @Schema(description = "所属城市ID")
    @TableField("city_id")
    private Integer cityId;

    @Schema(description = "产品名称")
    @TableField("name")
    private String name;

    @Schema(description = "产品类型(1讲解包 2景点)")
    @TableField("type")
    private Integer type;

    @Schema(description = "封面图URL")
    @TableField("cover_url")
    private String coverUrl;

    @Schema(description = "产品描述")
    @TableField("description")
    private String description;

    @Schema(description = "价格")
    @TableField("price")
    private BigDecimal price;

    @Schema(description = "讲解时长")
    @TableField("duration")
    private String duration;

    @Schema(description = "讲师ID")
    @TableField("lecturer_id")
    private Integer lecturerId;

    @Schema(description = "标签(逗号分隔)")
    @TableField("tags")
    private String tags;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
