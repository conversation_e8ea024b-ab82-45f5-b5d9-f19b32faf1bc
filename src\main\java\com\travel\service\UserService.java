package com.travel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.travel.entity.User;

/**
 * 用户信息服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface UserService extends IService<User> {

    /**
     * 根据openid查找用户
     */
    User findByOpenid(String openid);

    /**
     * 创建或更新用户信息
     */
    User createOrUpdateUser(String openid, String nickname, String avatarUrl, String region);

    /**
     * 更新用户手机号
     */
    void updatePhone(Integer userId, String phone);

    /**
     * 获取用户详细信息
     */
    User getUserDetail(Integer userId);
}
