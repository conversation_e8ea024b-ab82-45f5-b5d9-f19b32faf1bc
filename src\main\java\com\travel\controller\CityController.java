package com.travel.controller;

import com.travel.common.Result;
import com.travel.entity.City;
import com.travel.service.CityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 城市控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "城市管理", description = "城市相关接口")
@RestController
@RequestMapping("/city")
@RequiredArgsConstructor
public class CityController {

    private final CityService cityService;

    @Operation(summary = "获取所有城市", description = "获取系统中所有城市列表")
    @GetMapping("/list")
    public Result<List<City>> getAllCities() {
        List<City> cities = cityService.getAllCities();
        return Result.success(cities);
    }

    @Operation(summary = "获取城市详情", description = "根据城市ID获取详细信息")
    @GetMapping("/{cityId}")
    public Result<City> getCityDetail(
            @Parameter(description = "城市ID", required = true) @PathVariable Integer cityId) {
        City city = cityService.getCityDetail(cityId);
        return Result.success(city);
    }

    @Operation(summary = "搜索城市", description = "根据城市名称搜索")
    @GetMapping("/search")
    public Result<List<City>> searchCities(
            @Parameter(description = "城市名称", required = true) @RequestParam String name) {
        List<City> cities = cityService.searchCitiesByName(name);
        return Result.success(cities);
    }
}
