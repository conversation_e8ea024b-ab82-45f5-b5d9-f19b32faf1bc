package com.travel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.travel.entity.AttractionCategory;

import java.util.List;

/**
 * 景区分类服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface AttractionCategoryService extends IService<AttractionCategory> {

    /**
     * 根据产品ID获取分类列表
     */
    List<AttractionCategory> getCategoriesByProductId(Integer productId);

    /**
     * 获取分类详情
     */
    AttractionCategory getCategoryDetail(Integer categoryId);
}
