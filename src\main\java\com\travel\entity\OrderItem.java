package com.travel.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单明细实体
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@TableName("order_item")
@Schema(description = "订单明细")
public class OrderItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "明细ID")
    @TableId(value = "item_id", type = IdType.AUTO)
    private Integer itemId;

    @Schema(description = "订单ID")
    @TableField("order_id")
    private Integer orderId;

    @Schema(description = "产品ID")
    @TableField("product_id")
    private Integer productId;

    @Schema(description = "SKU名称")
    @TableField("sku_name")
    private String skuName;

    @Schema(description = "单价")
    @TableField("price")
    private BigDecimal price;

    @Schema(description = "数量")
    @TableField("quantity")
    private Integer quantity;
}
