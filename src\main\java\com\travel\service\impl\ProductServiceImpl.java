package com.travel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.common.BusinessException;
import com.travel.common.ResultCode;
import com.travel.entity.Product;
import com.travel.mapper.ProductMapper;
import com.travel.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 产品信息服务实现
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Override
    public IPage<Product> getProductPage(Integer current, Integer size, Integer cityId, Integer type, String keyword) {
        Page<Product> page = new Page<>(current, size);
        return this.baseMapper.selectProductPageWithDetails(page, cityId, type, keyword);
    }

    @Override
    public Product getProductDetail(Integer productId) {
        Product product = this.getById(productId);
        if (product == null) {
            throw BusinessException.of(ResultCode.PRODUCT_NOT_FOUND);
        }
        return product;
    }

    @Override
    public IPage<Product> getProductsByCity(Integer current, Integer size, Integer cityId) {
        Page<Product> page = new Page<>(current, size);
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<Product>()
                .eq(Product::getCityId, cityId)
                .orderByDesc(Product::getCreatedAt);
        return this.page(page, wrapper);
    }

    @Override
    public IPage<Product> getHotProducts(Integer current, Integer size) {
        Page<Product> page = new Page<>(current, size);
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<Product>()
                .orderByDesc(Product::getCreatedAt)
                .last("LIMIT " + size);
        return this.page(page, wrapper);
    }

    @Override
    public IPage<Product> searchProducts(Integer current, Integer size, String keyword) {
        Page<Product> page = new Page<>(current, size);
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(keyword)) {
            wrapper.like(Product::getName, keyword)
                   .or()
                   .like(Product::getDescription, keyword)
                   .or()
                   .like(Product::getTags, keyword);
        }
        
        wrapper.orderByDesc(Product::getCreatedAt);
        return this.page(page, wrapper);
    }
}
