package com.travel.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.travel.common.PageResult;
import com.travel.common.Result;
import com.travel.entity.Product;
import com.travel.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 产品控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "产品管理", description = "产品相关接口")
@RestController
@RequestMapping("/product")
@RequiredArgsConstructor
public class ProductController {

    private final ProductService productService;

    @Operation(summary = "分页查询产品列表", description = "支持按城市、类型、关键词筛选")
    @GetMapping("/page")
    public Result<PageResult<Product>> getProductPage(
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "城市ID") @RequestParam(required = false) Integer cityId,
            @Parameter(description = "产品类型(1讲解包 2景点)") @RequestParam(required = false) Integer type,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        
        IPage<Product> page = productService.getProductPage(current, size, cityId, type, keyword);
        return Result.success(PageResult.of(page));
    }

    @Operation(summary = "获取产品详情", description = "根据产品ID获取详细信息")
    @GetMapping("/{productId}")
    public Result<Product> getProductDetail(
            @Parameter(description = "产品ID", required = true) @PathVariable Integer productId) {
        Product product = productService.getProductDetail(productId);
        return Result.success(product);
    }

    @Operation(summary = "根据城市获取产品", description = "获取指定城市的产品列表")
    @GetMapping("/city/{cityId}")
    public Result<PageResult<Product>> getProductsByCity(
            @Parameter(description = "城市ID", required = true) @PathVariable Integer cityId,
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<Product> page = productService.getProductsByCity(current, size, cityId);
        return Result.success(PageResult.of(page));
    }

    @Operation(summary = "获取热门产品", description = "获取热门推荐产品列表")
    @GetMapping("/hot")
    public Result<PageResult<Product>> getHotProducts(
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<Product> page = productService.getHotProducts(current, size);
        return Result.success(PageResult.of(page));
    }

    @Operation(summary = "搜索产品", description = "根据关键词搜索产品")
    @GetMapping("/search")
    public Result<PageResult<Product>> searchProducts(
            @Parameter(description = "搜索关键词", required = true) @RequestParam String keyword,
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<Product> page = productService.searchProducts(current, size, keyword);
        return Result.success(PageResult.of(page));
    }
}
