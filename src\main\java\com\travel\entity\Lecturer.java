package com.travel.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 讲师信息实体
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@TableName("lecturer")
@Schema(description = "讲师信息")
public class Lecturer implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "讲师ID")
    @TableId(value = "lecturer_id", type = IdType.AUTO)
    private Integer lecturerId;

    @Schema(description = "讲师姓名")
    @TableField("name")
    private String name;

    @Schema(description = "讲师头像URL")
    @TableField("avatar_url")
    private String avatarUrl;

    @Schema(description = "头衔")
    @TableField("title")
    private String title;

    @Schema(description = "讲师简介")
    @TableField("intro")
    private String intro;

    @Schema(description = "专长领域")
    @TableField("expertise")
    private String expertise;
}
