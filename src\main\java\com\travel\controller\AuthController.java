package com.travel.controller;

import com.travel.common.Result;
import com.travel.dto.auth.PhoneAuthRequest;
import com.travel.dto.auth.WeChatLoginRequest;
import com.travel.service.AuthService;
import com.travel.vo.auth.LoginResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "认证管理", description = "用户认证相关接口")
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    @Operation(summary = "微信小程序登录", description = "通过微信授权code进行登录")
    @PostMapping("/wechat/login")
    public Result<LoginResponse> wechatLogin(@Valid @RequestBody WeChatLoginRequest request) {
        LoginResponse response = authService.wechatLogin(request);
        return Result.success("登录成功", response);
    }

    @Operation(summary = "手机号授权", description = "授权获取用户手机号")
    @PostMapping("/phone/authorize")
    public Result<Void> authorizePhone(
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId,
            @Valid @RequestBody PhoneAuthRequest request) {
        authService.authorizePhone(userId, request);
        return Result.success("手机号授权成功");
    }

    @Operation(summary = "刷新令牌", description = "刷新访问令牌")
    @PostMapping("/refresh")
    public Result<LoginResponse> refreshToken(HttpServletRequest request) {
        String token = extractToken(request);
        LoginResponse response = authService.refreshToken(token);
        return Result.success("令牌刷新成功", response);
    }

    @Operation(summary = "用户登出", description = "用户登出，清除会话信息")
    @PostMapping("/logout")
    public Result<Void> logout(@Parameter(description = "用户ID", required = true) @RequestParam Integer userId) {
        authService.logout(userId);
        return Result.success("登出成功");
    }

    /**
     * 从请求头中提取token
     */
    private String extractToken(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
