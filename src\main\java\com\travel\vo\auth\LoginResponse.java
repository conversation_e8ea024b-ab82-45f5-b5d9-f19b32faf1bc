package com.travel.vo.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 登录响应VO
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@Schema(description = "登录响应")
public class LoginResponse {

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String accessToken;

    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";

    @Schema(description = "用户ID", example = "1")
    private Integer userId;

    @Schema(description = "用户昵称", example = "张三")
    private String nickname;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "是否需要手机号授权", example = "false")
    private Boolean needPhoneAuth;

    public LoginResponse(String accessToken, Integer userId, String nickname, String avatarUrl, Boolean needPhoneAuth) {
        this.accessToken = accessToken;
        this.userId = userId;
        this.nickname = nickname;
        this.avatarUrl = avatarUrl;
        this.needPhoneAuth = needPhoneAuth;
    }
}
