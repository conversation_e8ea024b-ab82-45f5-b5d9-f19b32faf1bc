package com.travel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.common.BusinessException;
import com.travel.common.ResultCode;
import com.travel.entity.City;
import com.travel.mapper.CityMapper;
import com.travel.service.CityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 城市信息服务实现
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class CityServiceImpl extends ServiceImpl<CityMapper, City> implements CityService {

    @Override
    public List<City> getAllCities() {
        return this.list();
    }

    @Override
    public City getCityDetail(Integer cityId) {
        City city = this.getById(cityId);
        if (city == null) {
            throw BusinessException.of(ResultCode.NOT_FOUND, "城市不存在");
        }
        return city;
    }

    @Override
    public List<City> searchCitiesByName(String name) {
        if (StringUtils.isBlank(name)) {
            return getAllCities();
        }
        
        LambdaQueryWrapper<City> wrapper = new LambdaQueryWrapper<City>()
                .like(City::getName, name);
        return this.list(wrapper);
    }
}
