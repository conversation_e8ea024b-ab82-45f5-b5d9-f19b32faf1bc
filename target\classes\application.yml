server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: miniprogram-backend
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: 旅游讲解小程序API文档
    description: 微信小程序旅游讲解后端服务API接口文档
    version: 1.0.0
    concat: <EMAIL>
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表

# 微信小程序配置
wechat:
  miniapp:
    app-id: ${WECHAT_APPID:your_app_id}
    secret: ${WECHAT_SECRET:your_secret}

# JWT配置
jwt:
  secret: ${JWT_SECRET:travel_miniprogram_secret_key_2024}
  expiration: *********  # 7天，单位毫秒
  header: Authorization
  prefix: Bearer

# 日志配置
logging:
  level:
    com.travel: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/travel-backend.log

# 文件上传配置
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/uploads}
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,mp3,mp4,pdf

# 业务配置
business:
  # 订单配置
  order:
    timeout: 1800000  # 30分钟，单位毫秒
  # 分页配置
  page:
    default-size: 10
    max-size: 100
