package com.travel.service;

import com.travel.dto.auth.PhoneAuthRequest;
import com.travel.dto.auth.WeChatLoginRequest;
import com.travel.vo.auth.LoginResponse;

/**
 * 认证服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface AuthService {

    /**
     * 微信小程序登录
     */
    LoginResponse wechatLogin(WeChatLoginRequest request);

    /**
     * 手机号授权
     */
    void authorizePhone(Integer userId, PhoneAuthRequest request);

    /**
     * 刷新令牌
     */
    LoginResponse refreshToken(String token);

    /**
     * 登出
     */
    void logout(Integer userId);
}
