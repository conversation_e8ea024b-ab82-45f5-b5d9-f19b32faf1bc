package com.travel.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 城市信息实体
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@TableName("city")
@Schema(description = "城市信息")
public class City implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "城市ID")
    @TableId(value = "city_id", type = IdType.AUTO)
    private Integer cityId;

    @Schema(description = "城市名称")
    @TableField("name")
    private String name;

    @Schema(description = "天气信息")
    @TableField("weather")
    private String weather;

    @Schema(description = "城市Banner图")
    @TableField("banner_url")
    private String bannerUrl;

    @Schema(description = "宣传标语")
    @TableField("slogan")
    private String slogan;
}
